import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// Steam API proxy to securely handle Steam Web API calls
// This keeps the Steam API key server-side and avoids CORS issues

interface SteamApiRequest {
  endpoint: 'profile' | 'library' | 'game-details';
  steamId?: string;
  appId?: number;
}

const STEAM_API_KEY = Deno.env.get('STEAM_API_KEY');

if (!STEAM_API_KEY) {
  console.error('STEAM_API_KEY environment variable is not set');
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
};

async function handleSteamProfileRequest(steamId: string) {
  const url = `https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v0002/?key=${STEAM_API_KEY}&steamids=${steamId}&format=json`;
  
  console.log(`Fetching Steam profile for ID: ${steamId}`);
  
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`Steam API error: ${response.status} ${response.statusText}`);
  }
  
  const data = await response.json();
  
  if (!data.response || !data.response.players || data.response.players.length === 0) {
    throw new Error('Steam profile not found');
  }
  
  const profile = data.response.players[0];
  console.log(`✅ Found Steam profile: ${profile.personaname}`);
  
  return {
    success: true,
    data: profile
  };
}

async function handleSteamLibraryRequest(steamId: string) {
  const url = `https://api.steampowered.com/IPlayerService/GetOwnedGames/v0001/?key=${STEAM_API_KEY}&steamid=${steamId}&format=json&include_appinfo=true&include_played_free_games=true`;
  
  console.log(`Fetching Steam library for ID: ${steamId}`);
  
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`Steam API error: ${response.status} ${response.statusText}`);
  }
  
  const data = await response.json();
  
  if (!data.response || !data.response.games) {
    throw new Error('No games found or Steam profile is private');
  }
  
  console.log(`✅ Found ${data.response.games.length} games in Steam library`);
  
  return {
    success: true,
    data: data.response.games,
    count: data.response.game_count || data.response.games.length
  };
}

async function handleGameDetailsRequest(appId: number) {
  const url = `https://store.steampowered.com/api/appdetails?appids=${appId}&format=json`;
  
  console.log(`Fetching Steam game details for App ID: ${appId}`);
  
  const response = await fetch(url);
  
  if (!response.ok) {
    console.warn(`Steam Store API error for app ${appId}: ${response.status}`);
    return {
      success: false,
      data: null,
      error: `Steam Store API error: ${response.status}`
    };
  }
  
  const data = await response.json();
  const appData = data[appId];
  
  if (!appData || !appData.success || !appData.data) {
    console.warn(`No data found for Steam app ${appId}`);
    return {
      success: false,
      data: null,
      error: 'No game data found'
    };
  }
  
  return {
    success: true,
    data: appData.data
  };
}

Deno.serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }

  try {
    // Validate Steam API key
    if (!STEAM_API_KEY) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Steam API key not configured on server' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Parse request body
    const body: SteamApiRequest = await req.json();
    
    if (!body.endpoint) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Missing endpoint parameter' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    let result;

    switch (body.endpoint) {
      case 'profile':
        if (!body.steamId) {
          throw new Error('steamId is required for profile endpoint');
        }
        result = await handleSteamProfileRequest(body.steamId);
        break;

      case 'library':
        if (!body.steamId) {
          throw new Error('steamId is required for library endpoint');
        }
        result = await handleSteamLibraryRequest(body.steamId);
        break;

      case 'game-details':
        if (!body.appId) {
          throw new Error('appId is required for game-details endpoint');
        }
        result = await handleGameDetailsRequest(body.appId);
        break;

      default:
        throw new Error(`Unknown endpoint: ${body.endpoint}`);
    }

    return new Response(
      JSON.stringify(result),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Steam proxy error:', error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});