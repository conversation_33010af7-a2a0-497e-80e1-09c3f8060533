{"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": true, "autoApprove": []}, "shadcn-ui": {"command": "npx", "args": ["@jpisnice/shadcn-ui-mcp-server"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "**************************"}, "disabled": false, "autoApprove": ["get_component", "get_component"]}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--project-ref=kfzwgkzvlbyxotnbhgqk"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}, "disabled": false, "autoApprove": ["list_tables", "execute_sql"]}}}