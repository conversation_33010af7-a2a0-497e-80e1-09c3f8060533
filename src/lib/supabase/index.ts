// Main Supabase module exports
export { supabase } from './supabase-client';
export { auth } from './supabase-auth';
export { storage } from './supabase-storage';
export { 
  logError, 
  withRetry, 
  isSupabaseError, 
  getErrorMessage, 
  validateEnvironment 
} from './supabase-utils';

// Database operations
export {
  ensureUserRecords,
  games,
  userGames,
  priceTracking,
  userProfiles,
  userPreferences,
  stats,
  recommendations
} from './supabase-database';

// Tagging and filtering operations
export {
  userTags,
  userGameTags,
  filterPresets,
  tagSuggestions
} from './supabase-tags';

// Steam-specific operations
export {
  userGameAchievements,
  steamFriends,
  steamPlayerBans,
  userGameStats,
  steamGameNews,
  steamWorkshopItems
} from './supabase-steam';

// Legacy compatibility - create a db object that matches the original structure
import {
  games,
  userGames,
  priceTracking,
  userProfiles,
  userPreferences,
  stats,
  recommendations
} from './supabase-database';

import {
  userGameAchievements,
  steamFriends,
  steamPlayerBans,
  userGameStats,
  steamGameNews,
  steamWorkshopItems
} from './supabase-steam';

export const db = {
  games,
  userGames,
  priceTracking,
  userProfiles,
  userPreferences,
  stats,
  recommendations,
  // Steam-specific operations
  userGameAchievements,
  steamFriends,
  steamPlayerBans,
  userGameStats,
  steamGameNews,
  steamWorkshopItems,
  // Add supabase client for direct access when needed
  supabase: require('./supabase-client').supabase
};