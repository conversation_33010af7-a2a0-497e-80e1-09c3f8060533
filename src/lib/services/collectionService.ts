import { userGamesAPI, smartCollectionsAPI } from '../api/collections';
import { gameService } from './gameService';
import { Game, Platform } from '../../types';
import { UserGameRecord } from '../../types/database';

/**
 * Collection Service - Business logic for game collection management
 * Handles complex collection workflows and smart collection generation
 */
export class CollectionService {
  /**
   * Add game to collection with intelligent status detection
   */
  async addGameToCollection(userId: string, gameId: string, options: {
    status?: 'owned' | 'playing' | 'completed' | 'backlog' | 'wishlist';
    platform?: Platform;
    personalRating?: number;
    notes?: string;
    autoDetectStatus?: boolean;
  } = {}) {
    const { status = 'owned', platform, personalRating, notes, autoDetectStatus = true } = options;

    try {
      // Check if game already exists in collection
      const existingGame = await userGamesAPI.getByUserAndGame(userId, gameId);
      
      if (existingGame.data) {
        // Update existing game
        return await userGamesAPI.updateStatus(existingGame.data.id, {
          status,
          platform,
          personal_rating: personalRating,
          notes,
          updated_at: new Date().toISOString()
        });
      }

      // Get game details for better categorization
      let detectedStatus = status;
      if (autoDetectStatus) {
        const gameDetails = await gameService.getGameDetails(gameId);
        detectedStatus = this.detectOptimalStatus(gameDetails, status);
      }

      // Add new game to collection
      const gameData: Omit<UserGameRecord, 'id' | 'created_at' | 'updated_at'> = {
        user_id: userId,
        game_id: gameId,
        status: detectedStatus,
        platform,
        personal_rating: personalRating,
        notes,
        date_added: new Date().toISOString(),
        last_played: null,
        hours_played: null,
        achievements_unlocked: null,
        completion_percentage: null,
        is_favorite: false,
        purchase_price: null,
        purchase_date: null,
        tags: []
      };

      return await userGamesAPI.addToCollection(gameData);
    } catch (error) {
      console.error('Error adding game to collection:', error);
      throw new Error('Failed to add game to collection');
    }
  }

  /**
   * Move game between collection statuses with validation
   */
  async moveGameStatus(userId: string, gameId: string, newStatus: 'owned' | 'playing' | 'completed' | 'backlog' | 'wishlist', options: {
    updatePlayTime?: boolean;
    completionDate?: string;
    rating?: number;
  } = {}) {
    const { updatePlayTime = true, completionDate, rating } = options;

    try {
      const updateData: Partial<UserGameRecord> = {
        status: newStatus,
        updated_at: new Date().toISOString()
      };

      // Add status-specific updates
      if (newStatus === 'completed') {
        updateData.completion_percentage = 100;
        if (completionDate) {
          updateData.last_played = completionDate;
        }
        if (rating) {
          updateData.personal_rating = rating;
        }
      } else if (newStatus === 'playing') {
        if (updatePlayTime) {
          updateData.last_played = new Date().toISOString();
        }
      }

      return await userGamesAPI.updateGameStatus(userId, gameId, updateData);
    } catch (error) {
      console.error('Error moving game status:', error);
      throw new Error('Failed to update game status');
    }
  }

  /**
   * Get smart collections for user
   */
  async getSmartCollections(userId: string) {
    try {
      const [
        recentlyAdded,
        highlyRated,
        unfinishedGames,
        gamesToReplay
      ] = await Promise.allSettled([
        smartCollectionsAPI.getRecentlyAdded(userId, 10),
        smartCollectionsAPI.getHighlyRated(userId, 8, 10),
        smartCollectionsAPI.getUnfinishedGames(userId, 15),
        smartCollectionsAPI.getGamesToReplay(userId, 8, 8)
      ]);

      return {
        recentlyAdded: recentlyAdded.status === 'fulfilled' ? recentlyAdded.value.data || [] : [],
        highlyRated: highlyRated.status === 'fulfilled' ? highlyRated.value.data || [] : [],
        unfinishedGames: unfinishedGames.status === 'fulfilled' ? unfinishedGames.value.data || [] : [],
        gamesToReplay: gamesToReplay.status === 'fulfilled' ? gamesToReplay.value.data || [] : []
      };
    } catch (error) {
      console.error('Error getting smart collections:', error);
      return {
        recentlyAdded: [],
        highlyRated: [],
        unfinishedGames: [],
        gamesToReplay: []
      };
    }
  }

  /**
   * Generate collection insights and recommendations
   */
  async generateCollectionInsights(userId: string) {
    try {
      const { data: userGames, error } = await userGamesAPI.getUserCollection(userId);
      
      if (error) {
        throw new Error(`Failed to get user collection: ${error.message}`);
      }

      const games = userGames || [];
      
      // Analyze collection patterns
      const insights = {
        totalGames: games.length,
        completionRate: this.calculateCompletionRate(games),
        genreDistribution: this.analyzeGenreDistribution(games),
        platformDistribution: this.analyzePlatformDistribution(games),
        playingHabits: this.analyzePlayingHabits(games),
        recommendations: await this.generateCollectionRecommendations(games, userId)
      };

      return insights;
    } catch (error) {
      console.error('Error generating collection insights:', error);
      throw error;
    }
  }

  /**
   * Bulk operations for collection management
   */
  async bulkUpdateGames(userId: string, operations: Array<{
    gameId: string;
    operation: 'move_to_status' | 'rate' | 'add_tags' | 'remove';
    data: any;
  }>) {
    const results: Array<{ gameId: string; success: boolean; error?: string }> = [];

    for (const operation of operations) {
      try {
        switch (operation.operation) {
          case 'move_to_status':
            await this.moveGameStatus(userId, operation.gameId, operation.data.status, operation.data.options);
            results.push({ gameId: operation.gameId, success: true });
            break;

          case 'rate':
            await userGamesAPI.updateGameStatus(userId, operation.gameId, {
              personal_rating: operation.data.rating,
              updated_at: new Date().toISOString()
            });
            results.push({ gameId: operation.gameId, success: true });
            break;

          case 'add_tags':
            // This would require implementing tag functionality
            results.push({ gameId: operation.gameId, success: false, error: 'Tags not implemented' });
            break;

          case 'remove':
            await userGamesAPI.removeGameFromCollection(userId, operation.gameId);
            results.push({ gameId: operation.gameId, success: true });
            break;

          default:
            results.push({ gameId: operation.gameId, success: false, error: 'Unknown operation' });
        }
      } catch (error) {
        results.push({ 
          gameId: operation.gameId, 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return results;
  }

  /**
   * Export collection data
   */
  async exportCollection(userId: string, format: 'json' | 'csv' = 'json') {
    try {
      const { data: userGames, error } = await userGamesAPI.getUserCollection(userId);
      
      if (error) {
        throw new Error(`Failed to get user collection: ${error.message}`);
      }

      const games = userGames || [];

      if (format === 'json') {
        return {
          exportDate: new Date().toISOString(),
          totalGames: games.length,
          games: games.map(game => ({
            title: game.game?.title || 'Unknown',
            platform: game.platform,
            status: game.status,
            personalRating: game.personal_rating,
            dateAdded: game.date_added,
            lastPlayed: game.last_played,
            hoursPlayed: game.hours_played,
            notes: game.notes
          }))
        };
      } else if (format === 'csv') {
        const headers = ['Title', 'Platform', 'Status', 'Rating', 'Date Added', 'Last Played', 'Hours Played', 'Notes'];
        const rows = games.map(game => [
          game.game?.title || 'Unknown',
          game.platform || '',
          game.status || '',
          game.personal_rating?.toString() || '',
          game.date_added || '',
          game.last_played || '',
          game.hours_played?.toString() || '',
          game.notes || ''
        ]);

        return {
          headers,
          rows,
          csv: [headers, ...rows].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n')
        };
      }

      throw new Error('Unsupported export format');
    } catch (error) {
      console.error('Error exporting collection:', error);
      throw error;
    }
  }

  /**
   * Private helper methods
   */
  private detectOptimalStatus(game: Game | null, defaultStatus: string): 'owned' | 'playing' | 'completed' | 'backlog' | 'wishlist' {
    // Simple logic for status detection - can be enhanced with ML
    if (!game) return defaultStatus as any;

    // If it's a very short game (< 5 hours estimated), suggest playing
    if (game.description?.includes('short') || game.title.includes('Demo')) {
      return 'playing';
    }

    // If it's a very long game (RPG, Strategy), suggest backlog
    if (game.genres?.some(genre => ['RPG', 'Strategy', 'Simulation'].includes(genre))) {
      return 'backlog';
    }

    return defaultStatus as any;
  }

  private calculateCompletionRate(games: any[]): number {
    const ownedGames = games.filter(g => g.status !== 'wishlist');
    const completedGames = games.filter(g => g.status === 'completed');
    
    return ownedGames.length > 0 ? (completedGames.length / ownedGames.length) * 100 : 0;
  }

  private analyzeGenreDistribution(games: any[]): Array<{ genre: string; count: number; percentage: number }> {
    const genreCounts = new Map<string, number>();
    
    games.forEach(game => {
      if (game.game?.genres) {
        game.game.genres.forEach((genre: string) => {
          genreCounts.set(genre, (genreCounts.get(genre) || 0) + 1);
        });
      }
    });

    const total = games.length;
    return Array.from(genreCounts.entries())
      .map(([genre, count]) => ({
        genre,
        count,
        percentage: (count / total) * 100
      }))
      .sort((a, b) => b.count - a.count);
  }

  private analyzePlatformDistribution(games: any[]): Array<{ platform: string; count: number; percentage: number }> {
    const platformCounts = new Map<string, number>();
    
    games.forEach(game => {
      if (game.platform) {
        platformCounts.set(game.platform, (platformCounts.get(game.platform) || 0) + 1);
      }
    });

    const total = games.length;
    return Array.from(platformCounts.entries())
      .map(([platform, count]) => ({
        platform,
        count,
        percentage: (count / total) * 100
      }))
      .sort((a, b) => b.count - a.count);
  }

  private analyzePlayingHabits(games: any[]): {
    averageRating: number;
    mostPlayedGenre: string | null;
    preferredPlatform: string | null;
    completionTrend: 'improving' | 'declining' | 'stable';
  } {
    const ratedGames = games.filter(g => g.personal_rating);
    const averageRating = ratedGames.length > 0
      ? ratedGames.reduce((sum, g) => sum + g.personal_rating, 0) / ratedGames.length
      : 0;

    const genreDistribution = this.analyzeGenreDistribution(games);
    const platformDistribution = this.analyzePlatformDistribution(games);

    const mostPlayedGenre = genreDistribution[0]?.genre || null;
    const preferredPlatform = platformDistribution[0]?.platform || null;

    // Simple completion trend analysis
    const recentGames = games
      .filter(g => g.date_added)
      .sort((a, b) => new Date(b.date_added).getTime() - new Date(a.date_added).getTime())
      .slice(0, 10);

    const recentCompletionRate = this.calculateCompletionRate(recentGames);
    const overallCompletionRate = this.calculateCompletionRate(games);

    let completionTrend: 'improving' | 'declining' | 'stable' = 'stable';
    if (recentCompletionRate > overallCompletionRate + 5) {
      completionTrend = 'improving';
    } else if (recentCompletionRate < overallCompletionRate - 5) {
      completionTrend = 'declining';
    }

    return {
      averageRating,
      mostPlayedGenre,
      preferredPlatform,
      completionTrend
    };
  }

  private async generateCollectionRecommendations(games: any[], userId: string): Promise<string[]> {
    const recommendations: string[] = [];
    
    const completionRate = this.calculateCompletionRate(games);
    const genreDistribution = this.analyzeGenreDistribution(games);
    const playingHabits = this.analyzePlayingHabits(games);

    // Completion rate recommendations
    if (completionRate < 30) {
      recommendations.push('Consider focusing on shorter games to improve your completion rate');
    } else if (completionRate > 80) {
      recommendations.push('Great completion rate! You might enjoy more challenging or longer games');
    }

    // Genre diversity recommendations
    if (genreDistribution.length < 3) {
      recommendations.push('Try exploring different genres to diversify your gaming experience');
    }

    // Platform recommendations
    const platformDistribution = this.analyzePlatformDistribution(games);
    if (platformDistribution.length === 1) {
      recommendations.push('Consider trying games on different platforms for variety');
    }

    // Backlog recommendations
    const backlogGames = games.filter(g => g.status === 'backlog');
    if (backlogGames.length > 20) {
      recommendations.push('Your backlog is getting large - consider focusing on completing some games');
    }

    // Rating recommendations
    if (playingHabits.averageRating > 0 && playingHabits.averageRating < 3) {
      recommendations.push('Consider being more selective with game choices to improve satisfaction');
    }

    return recommendations;
  }
}

export const collectionService = new CollectionService();