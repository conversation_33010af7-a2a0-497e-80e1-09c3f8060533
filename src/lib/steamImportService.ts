import { db } from './supabase';

// Steam API response interfaces
interface SteamGameDetails {
  type?: string;
  name?: string;
  steam_appid?: number;
  short_description?: string;
  header_image?: string;
  developers?: string[];
  publishers?: string[];
  genres?: Array<{ description: string }>;
  platforms?: { windows?: boolean; mac?: boolean; linux?: boolean };
  metacritic?: { score?: number };
  screenshots?: Array<{ path_thumbnail: string }>;
  release_date?: { date?: string };
  price_overview?: {
    currency?: string;
    initial?: number;
    final?: number;
  };
}

interface SteamGame {
  appid: number;
  name: string;
  playtime_forever: number;
  img_icon_url?: string;
  img_logo_url?: string;
  has_community_visible_stats?: boolean;
  playtime_windows_forever?: number;
  playtime_mac_forever?: number;
  playtime_linux_forever?: number;
  rtime_last_played?: number;
}

interface SteamProfile {
  steamid: string;
  communityvisibilitystate: number;
  profilestate: number;
  personaname: string;
  profileurl: string;
  avatar: string;
  avatarmedium: string;
  avatarfull: string;
  personastate: number;
  realname?: string;
  timecreated?: number;
}

interface ImportProgress {
  phase: 'authenticating' | 'fetching_profile' | 'fetching_games' | 'processing' | 'saving' | 'complete';
  progress: number;
  message: string;
  gamesProcessed?: number;
  totalGames?: number;
}

class SteamImportService {
  private readonly STEAM_API_BASE = 'https://api.steampowered.com';
  private readonly STEAM_STORE_BASE = 'https://store.steampowered.com/api';
  
  /**
   * Get Steam profile information using Steam ID
   */
  async getSteamProfile(steamId: string): Promise<SteamProfile | null> {
    try {
      console.log(`Fetching Steam profile for: ${steamId}`);
      
      // Use Supabase Edge Function for secure Steam API proxy
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const response = await fetch(
        `${supabaseUrl}/functions/v1/steam-proxy`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
          },
          body: JSON.stringify({
            endpoint: 'profile',
            steamId: steamId
          })
        }
      );

      if (!response.ok) {
        throw new Error(`Steam proxy error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch Steam profile');
      }

      const profile = result.data;
      console.log(`✅ Found Steam profile: ${profile.personaname}`);
      
      return {
        steamid: profile.steamid,
        communityvisibilitystate: profile.communityvisibilitystate,
        profilestate: profile.profilestate,
        personaname: profile.personaname,
        profileurl: profile.profileurl,
        avatar: profile.avatar,
        avatarmedium: profile.avatarmedium,
        avatarfull: profile.avatarfull,
        personastate: profile.personastate,
        realname: profile.realname,
        timecreated: profile.timecreated
      };
    } catch (error) {
      console.error('Error fetching Steam profile:', error);
      return null;
    }
  }

  /**
   * Get user's Steam game library
   */
  async getSteamLibrary(steamId: string): Promise<SteamGame[]> {
    try {
      console.log(`Fetching actual Steam library for: ${steamId}`);
      
      // Use Supabase Edge Function for secure Steam API proxy
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const response = await fetch(
        `${supabaseUrl}/functions/v1/steam-proxy`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
          },
          body: JSON.stringify({
            endpoint: 'library',
            steamId: steamId
          })
        }
      );

      if (!response.ok) {
        throw new Error(`Steam proxy error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch Steam library');
      }

      const games: SteamGame[] = result.data.map((game: SteamGame) => ({
        appid: game.appid,
        name: game.name,
        playtime_forever: game.playtime_forever || 0,
        img_icon_url: game.img_icon_url || '',
        img_logo_url: game.img_logo_url || '',
        rtime_last_played: game.rtime_last_played || 0,
        playtime_windows_forever: game.playtime_windows_forever || 0,
        playtime_mac_forever: game.playtime_mac_forever || 0,
        playtime_linux_forever: game.playtime_linux_forever || 0,
        has_community_visible_stats: game.has_community_visible_stats || false
      }));

      console.log(`✅ Found ${games.length} games in Steam library`);
      return games;
    } catch (error) {
      console.error('Error fetching Steam library:', error);
      throw error;
    }
  }

  /**
   * Get detailed game information from Steam
   */
  async getSteamGameDetails(appId: number): Promise<SteamGameDetails> {
    try {
      console.log(`Fetching Steam game details for App ID: ${appId}`);
      
      // Use Supabase Edge Function for secure Steam API proxy
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const response = await fetch(
        `${supabaseUrl}/functions/v1/steam-proxy`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
          },
          body: JSON.stringify({
            endpoint: 'game-details',
            appId: appId
          })
        }
      );

      if (!response.ok) {
        console.warn(`Steam proxy error for app ${appId}: ${response.status}`);
        return {};
      }

      const result = await response.json();
      
      if (!result.success || !result.data) {
        console.warn(`No data found for Steam app ${appId}: ${result.error || 'Unknown error'}`);
        return {};
      }

      const gameData = result.data;
      return {
        type: gameData.type,
        name: gameData.name,
        steam_appid: gameData.steam_appid,
        short_description: gameData.short_description,
        header_image: gameData.header_image,
        developers: gameData.developers || [],
        publishers: gameData.publishers || [],
        genres: gameData.genres || [],
        platforms: gameData.platforms || {},
        metacritic: gameData.metacritic || {},
        screenshots: gameData.screenshots || [],
        release_date: gameData.release_date || {},
        price_overview: gameData.price_overview || {}
      };
    } catch (error) {
      console.error(`Error fetching Steam game details for app ${appId}:`, error);
      return {};
    }
  }

  /**
   * Import Steam library to user's collection
   */
  async importSteamLibrary(
    userId: string, 
    steamId: string,
    onProgress?: (progress: ImportProgress) => void
  ): Promise<{ success: boolean; imported: number; errors: string[]; message?: string }> {
    const errors: string[] = [];
    let imported = 0;

    try {
      // Phase 1: Authentication and profile fetch
      onProgress?.({
        phase: 'authenticating',
        progress: 10,
        message: 'Connecting to Steam...'
      });

      const profile = await this.getSteamProfile(steamId);
      if (!profile) {
        throw new Error('Failed to fetch Steam profile. Please check your Steam ID and ensure your profile is public.');
      }
      
      // Check if profile is public
      if (profile.communityvisibilitystate !== 3) {
        throw new Error('Your Steam profile must be public to import games. Please set your profile to public in Steam and try again.');
      }

      onProgress?.({
        phase: 'fetching_profile',
        progress: 20,
        message: `Found profile: ${profile.personaname}`
      });

      // Phase 2: Fetch game library
      onProgress?.({
        phase: 'fetching_games',
        progress: 30,
        message: 'Fetching your Steam library...'
      });

      const steamGames = await this.getSteamLibrary(steamId);
      if (steamGames.length === 0) {
        throw new Error('No games found in Steam library. Please ensure your game library is public in Steam Privacy Settings.');
      }

      onProgress?.({
        phase: 'processing',
        progress: 40,
        message: `Processing ${steamGames.length} games...`,
        totalGames: steamGames.length
      });

      // Phase 3: Process and save games
      for (let i = 0; i < steamGames.length; i++) {
        const steamGame = steamGames[i];
        
        try {
          onProgress?.({
            phase: 'processing',
            progress: 40 + (i / steamGames.length) * 50,
            message: `Processing: ${steamGame.name}`,
            gamesProcessed: i + 1,
            totalGames: steamGames.length
          });

          // Get detailed game information
          const gameDetails = await this.getSteamGameDetails(steamGame.appid);
          
          // Efficiently check if game already exists by Steam App ID
          let gameId: string;
          const { data: existingGameRecord } = await db.games.getBySteamAppId(steamGame.appid.toString());

          if (existingGameRecord) {
            gameId = existingGameRecord.id;
            console.log(`🔄 Found existing game: ${steamGame.name} (ID: ${gameId})`);
          } else {
            // Create new game entry with full Steam support
            const newGameData = {
              title: steamGame.name || 'Unknown Game',  // Ensure title is not empty
              platform: 'PC',  // Required field for Steam games
              description: gameDetails?.short_description || '',
              cover_image: gameDetails?.header_image || `https://cdn.akamai.steamstatic.com/steam/apps/${steamGame.appid}/header.jpg`,
              release_date: this.parseSteamReleaseDate(gameDetails?.release_date?.date),
              developer: gameDetails?.developers?.[0] || null,
              publisher: gameDetails?.publishers?.[0] || null,
              genres: gameDetails?.genres?.map((g: { description: string }) => g.description) || [],
              metacritic_score: this.validateMetacriticScore(gameDetails?.metacritic?.score),
              screenshots: gameDetails?.screenshots?.map((s: { path_thumbnail: string }) => s.path_thumbnail) || [],
              igdb_id: null,
              steam_app_id: steamGame.appid.toString(),
              steam_url: `https://store.steampowered.com/app/${steamGame.appid}`
            };

            // Validate required fields before attempting to create
            if (!newGameData.title || !newGameData.platform) {
              errors.push(`Invalid game data for: ${steamGame.name} (missing required fields)`);
              continue;
            }

            // Debug logging - see exactly what we're sending to the database
            console.log(`🔍 Creating game "${steamGame.name}" with data:`, JSON.stringify(newGameData, null, 2));

            const { data: createdGame, error: createError } = await db.games.upsert(newGameData);
            if (createError) {
              console.error(`❌ Failed to create game "${steamGame.name}":`, {
                error: createError,
                errorMessage: createError.message,
                errorDetails: createError.details,
                errorHint: createError.hint,
                errorCode: createError.code,
                gameData: newGameData,
                steamAppId: steamGame.appid
              });
              
              // More detailed error message
              const errorMsg = createError.message || createError.details || 'Unknown database error';
              errors.push(`Failed to create game: ${steamGame.name} - ${errorMsg}`);
              continue;
            }
            gameId = createdGame.id;
            console.log(`✅ Created game: ${steamGame.name} (ID: ${gameId})`);
          }

          // Check if user already has this game in their collection
          const { data: existingUserGame } = await db.userGames.getByUserAndGame(userId, gameId);
          
          const userGameData = {
            user_id: userId,
            game_id: gameId,
            status: this.determineGameStatus(steamGame),
            hours_played: Math.max(0, Math.round(steamGame.playtime_forever / 60)),
            personal_notes: `Imported from Steam. App ID: ${steamGame.appid}${steamGame.has_community_visible_stats ? '. Has Steam achievements.' : ''}`,
            date_added: existingUserGame?.date_added || new Date().toISOString(),
            import_source: 'steam',
            last_played: steamGame.rtime_last_played ? new Date(steamGame.rtime_last_played * 1000).toISOString() : null
          };

          const { error: addError } = await db.userGames.upsertToCollection(userGameData);
          if (addError) {
            // Log detailed error information for debugging
            console.error(`❌ Failed to add/update "${steamGame.name}" in collection:`, {
              error: addError,
              errorCode: addError.code,
              errorMessage: addError.message,
              errorDetails: addError.details,
              userGameData: userGameData,
              userId: userId,
              gameId: gameId
            });
            errors.push(`Failed to add to collection: ${steamGame.name} (${addError.message || addError.code || 'Unknown error'})`);
            continue;
          }

          imported++;
          const action = existingUserGame ? 'Updated' : 'Added';
          console.log(`✅ ${action} in collection: ${steamGame.name} (${Math.round(steamGame.playtime_forever / 60)}h played)`);

          // Small delay to prevent overwhelming the API
          await new Promise(resolve => setTimeout(resolve, 100));

        } catch (gameError) {
          console.error(`Error processing game ${steamGame.name}:`, gameError);
          errors.push(`Error processing: ${steamGame.name}`);
        }
      }

      onProgress?.({
        phase: 'complete',
        progress: 100,
        message: `Import complete! Added ${imported} games to your collection.`
      });

      // Provide helpful summary message
      const successMessage = imported > 0 
        ? `Successfully imported ${imported} games from Steam!` 
        : 'No new games were imported (all games may already be in your library).';
      
      return {
        success: true,
        imported,
        errors,
        message: successMessage
      };

    } catch (error) {
      console.error('Steam import error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      errors.push(errorMessage);
      
      // Provide helpful error messages based on common issues
      let userFriendlyMessage = errorMessage;
      if (errorMessage.includes('profile') && errorMessage.includes('public')) {
        userFriendlyMessage = 'Please set your Steam profile to public and try again.';
      } else if (errorMessage.includes('library') && errorMessage.includes('private')) {
        userFriendlyMessage = 'Please set your Steam game library to public in Privacy Settings and try again.';
      } else if (errorMessage.includes('Steam ID')) {
        userFriendlyMessage = 'Please check your Steam ID and try again.';
      }
      
      return {
        success: false,
        imported,
        errors,
        message: userFriendlyMessage
      };
    }
  }

  /**
   * Determine game status based on playtime
   */
  private determineGameStatus(steamGame: SteamGame): 'playing' | 'completed' | 'backlog' | 'wishlist' {
    const playtimeHours = steamGame.playtime_forever / 60;
    const lastPlayedDays = steamGame.rtime_last_played 
      ? (Date.now() / 1000 - steamGame.rtime_last_played) / 86400 
      : 999;

    if (playtimeHours === 0) {
      return 'backlog';
    } else if (lastPlayedDays <= 7 && playtimeHours > 2) {
      return 'playing';
    } else if (playtimeHours > 50) { // Assume completion for games with high playtime
      return 'completed';
    } else {
      return 'backlog'; // Changed from 'library' to 'backlog' as valid status
    }
  }

  /**
   * Extract platform information (currently unused but may be needed for future enhancements)
   */
  // private extractPlatforms(platforms: { windows?: boolean; mac?: boolean; linux?: boolean }): string[] {
  //   if (!platforms) return ['PC'];
  //   
  //   const platformList: string[] = [];
  //   if (platforms.windows) platformList.push('PC');
  //   if (platforms.mac) platformList.push('Mac');
  //   if (platforms.linux) platformList.push('Linux');
  //   
  //   return platformList.length > 0 ? platformList : ['PC'];
  // }

  /**
   * Parse and validate Steam release date
   */
  private parseSteamReleaseDate(dateString?: string): string | null {
    if (!dateString) return null;
    
    try {
      // Steam dates can be in various formats like "Dec 10, 2020" or "Coming Soon"
      if (dateString.toLowerCase().includes('coming soon') || dateString.toLowerCase().includes('tba')) {
        return null;
      }
      
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return null;
      }
      
      return date.toISOString().split('T')[0]; // Return YYYY-MM-DD format
    } catch (error) {
      console.warn(`Failed to parse release date: ${dateString}`, error);
      return null;
    }
  }

  /**
   * Validate Metacritic score
   */
  private validateMetacriticScore(score?: number): number | null {
    if (typeof score !== 'number' || isNaN(score)) return null;
    if (score < 0 || score > 100) return null;
    return Math.round(score);
  }

  /**
   * Validate Steam ID format
   */
  validateSteamId(steamId: string): boolean {
    // Steam ID can be:
    // - 64-bit Steam ID (17 digits)
    // - Custom URL (letters, numbers, underscore)
    // - Profile URL
    
    if (!steamId.trim()) return false;
    
    // Extract from URL if provided
    const urlMatch = steamId.match(/steamcommunity\.com\/(?:id|profiles)\/([^/]+)/);
    if (urlMatch) {
      steamId = urlMatch[1];
    }
    
    // Check for 64-bit Steam ID (17 digits starting with 7656119)
    if (/^7656119\d{10}$/.test(steamId)) {
      return true;
    }
    
    // Check for custom URL (3-32 characters, alphanumeric + underscore)
    if (/^[a-zA-Z0-9_]{3,32}$/.test(steamId)) {
      return true;
    }
    
    return false;
  }

  /**
   * Extract Steam ID from various input formats
   */
  extractSteamId(input: string): string {
    const trimmed = input.trim();
    
    // Extract from Steam profile URL
    const urlMatch = trimmed.match(/steamcommunity\.com\/(?:id|profiles)\/([^/]+)/);
    if (urlMatch) {
      return urlMatch[1];
    }
    
    return trimmed;
  }

  /**
   * Get import statistics for user
   */
  async getImportStats(userId: string): Promise<{
    totalImported: number;
    steamGames: number;
    epicGames: number;
    consoleGames: number;
    lastImport?: string;
  }> {
    try {
      const { data: userGames, error } = await db.userGames.getUserCollection(userId);
      if (error) throw error;

      const totalImported = userGames?.length || 0;
      
      // Use import_source field for accurate Steam game counting
      const steamGames = userGames?.filter(g => 
        g.import_source === 'steam' || // Primary check: import source
        g.personal_notes?.includes('Imported from Steam') || // Fallback for legacy imports
        g.personal_notes?.includes('App ID:')
      ).length || 0;
      
      // Count other import sources (currently 0 until we have proper tracking)
      const epicGames = 0;
      const consoleGames = 0;

      // Get most recent Steam import using import_source
      const steamImports = userGames?.filter(g => 
        g.import_source === 'steam' || // Primary check: import source
        g.personal_notes?.includes('Imported from Steam') || // Fallback for legacy imports
        g.personal_notes?.includes('App ID:')
      );
      const lastImport = steamImports && steamImports.length > 0 
        ? steamImports.sort((a, b) => new Date(b.date_added).getTime() - new Date(a.date_added).getTime())[0].date_added
        : undefined;

      return {
        totalImported,
        steamGames,
        epicGames,
        consoleGames,
        lastImport
      };
    } catch (error) {
      console.error('Error getting import stats:', error);
      return {
        totalImported: 0,
        steamGames: 0,
        epicGames: 0,
        consoleGames: 0
      };
    }
  }
}

export const steamImportService = new SteamImportService();
export type { SteamGame, SteamProfile, ImportProgress };