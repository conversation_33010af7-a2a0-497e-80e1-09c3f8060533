import { ViewMode } from '@/pages/Library/types';

/**
 * Utility functions for handling smooth transitions between view modes
 * while preserving user context like search queries and filters
 */

export interface ViewModeContext {
  searchQuery: string;
  sortOptions: { field: string; direction: 'asc' | 'desc' };
  filters: Record<string, any>;
  selectedGameId?: string;
  scrollPosition?: number;
}

/**
 * Preserves the current view context when switching between view modes
 */
export function preserveViewContext(
  fromMode: ViewMode,
  toMode: ViewMode,
  context: ViewModeContext
): ViewModeContext {
  // Base context that should always be preserved
  const preservedContext: ViewModeContext = {
    searchQuery: context.searchQuery,
    sortOptions: context.sortOptions,
    filters: context.filters,
    selectedGameId: context.selectedGameId,
  };

  // Handle specific transitions that might need special handling
  if (fromMode === 'platform' && toMode === 'platform-family') {
    // When switching from platform to platform-family, preserve all context
    // This is a natural transition that users might expect to maintain state
    return preservedContext;
  }

  if (fromMode === 'platform-family' && toMode === 'platform') {
    // When switching from platform-family to platform, preserve all context
    // Users might want to see the same filtered results in more detail
    return preservedContext;
  }

  if (fromMode === 'status' && (toMode === 'platform' || toMode === 'platform-family')) {
    // When switching from status view to platform views, preserve search and filters
    // but might need to adjust sort options if they don't make sense in the new context
    const adjustedSortOptions = adjustSortOptionsForViewMode(context.sortOptions, toMode);
    return {
      ...preservedContext,
      sortOptions: adjustedSortOptions,
    };
  }

  if ((fromMode === 'platform' || fromMode === 'platform-family') && toMode === 'status') {
    // When switching to status view, preserve search and filters
    // but adjust sort options for status-based sorting
    const adjustedSortOptions = adjustSortOptionsForViewMode(context.sortOptions, toMode);
    return {
      ...preservedContext,
      sortOptions: adjustedSortOptions,
    };
  }

  // Default: preserve all context
  return preservedContext;
}

/**
 * Adjusts sort options to be appropriate for the target view mode
 */
function adjustSortOptionsForViewMode(
  sortOptions: { field: string; direction: 'asc' | 'desc' },
  viewMode: ViewMode
): { field: string; direction: 'asc' | 'desc' } {
  const { field, direction } = sortOptions;

  // If the current sort field is appropriate for the new view mode, keep it
  const universalSortFields = ['title', 'date_added', 'release_date', 'metacritic_score'];
  if (universalSortFields.includes(field)) {
    return sortOptions;
  }

  // Adjust sort field based on view mode
  switch (viewMode) {
    case 'status':
      // For status view, default to status-based sorting if current field isn't universal
      if (field === 'status' || field === 'date_completed') {
        return sortOptions;
      }
      return { field: 'status', direction };

    case 'platform':
    case 'platform-family':
      // For platform views, default to platform-based sorting if current field isn't universal
      if (field === 'platform') {
        return sortOptions;
      }
      return { field: 'title', direction }; // Use title as a safe default

    default:
      return sortOptions;
  }
}

/**
 * Determines if a view mode transition should trigger a scroll reset
 */
export function shouldResetScrollOnTransition(fromMode: ViewMode, toMode: ViewMode): boolean {
  // Reset scroll when switching between fundamentally different organization modes
  if (fromMode === 'status' && (toMode === 'platform' || toMode === 'platform-family')) {
    return true;
  }
  
  if ((fromMode === 'platform' || fromMode === 'platform-family') && toMode === 'status') {
    return true;
  }

  // Don't reset scroll for transitions between platform and platform-family
  // as they're similar organizational structures
  return false;
}

/**
 * Gets a user-friendly transition message for view mode changes
 */
export function getViewModeTransitionMessage(fromMode: ViewMode, toMode: ViewMode): string | null {
  if (fromMode === toMode) return null;

  const modeNames = {
    status: 'Status View',
    platform: 'Platform View',
    'platform-family': 'Platform Family View'
  };

  return `Switched to ${modeNames[toMode]}`;
}

/**
 * Validates that a view mode transition is allowed
 */
export function isValidViewModeTransition(fromMode: ViewMode, toMode: ViewMode): boolean {
  const validModes: ViewMode[] = ['status', 'platform', 'platform-family'];
  return validModes.includes(fromMode) && validModes.includes(toMode);
}