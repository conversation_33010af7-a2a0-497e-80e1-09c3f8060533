import { useState, memo, useCallback, useEffect } from 'react';
import {
  Gamepad2,
  Star,
  Calendar,
  User,
  Loader2,
  AlertCircle,
  ArrowUp,
  Plus,
  Heart,
  CheckCircle
} from 'lucide-react';
import { Game } from '../../types';
import { GameModal } from './game-modal';
import { Card, CardContent } from '../base/card';
import { Badge } from '../base/badge';
import { GameGridSkeleton } from './game-card-skeleton';
import { Button } from '../base/button';
import { useAddToLibrary, useAddToWishlist } from '@/hooks/useGameActions';
import { useUserGameStatus } from '@/hooks/useUserCollection';

interface GameResultsProps {
  games: Game[];
  loading: boolean;
  error: string | null;
  onRetry?: () => void;
}

export function GameResults({ games, loading, error, onRetry }: GameResultsProps) {
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showBackToTop, setShowBackToTop] = useState(false);

  const handleGameClick = useCallback((game: Game) => {
    setSelectedGame(game);
    setIsModalOpen(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
    setSelectedGame(null);
  }, []);

  const scrollToTop = useCallback(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  if (loading) {
    return (
      <div className="w-full max-w-7xl mx-auto p-6">
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-6">
            <div className="p-3 bg-primary/10 rounded-2xl">
              <Loader2 className="h-8 w-8 text-primary animate-spin" />
            </div>
            <div>
              <h2 className="text-3xl font-bold bg-gradient-to-r from-primary to-orange-600 bg-clip-text text-transparent mb-2">
                Searching Games
              </h2>
              <p className="text-muted-foreground">Discovering games across all platforms...</p>
            </div>
          </div>
        </div>
        <GameGridSkeleton count={21} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full max-w-7xl mx-auto p-6">
        <Card className="border-destructive/20">
          <CardContent className="p-12">
            <div className="text-center">
              <div className="flex justify-center mb-4">
                <div className="p-4 bg-destructive/10 rounded-2xl">
                  <AlertCircle className="h-8 w-8 text-destructive" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-3">Search Error</h3>
              <p className="text-destructive max-w-md mx-auto leading-relaxed mb-4">
                {error}
              </p>
              <p className="text-muted-foreground max-w-md mx-auto leading-relaxed mb-6">
                Please try again with different search terms or check your internet connection.
              </p>
              {onRetry && (
                <Button onClick={onRetry} variant="outline" className="mt-4">
                  Try Again
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }


  return (
    <div className="w-full max-w-7xl mx-auto p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-primary/10 rounded-2xl">
              <Gamepad2 className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h2 className="text-3xl font-bold bg-gradient-to-r from-primary to-orange-600 bg-clip-text text-transparent mb-2">
                Search Results
              </h2>
              <p className="text-muted-foreground">
                {games.length === 0
                  ? "No games match your current filters"
                  : `Found ${games.length} ${games.length === 1 ? 'game' : 'games'}`
                }
              </p>
            </div>
          </div>

          {games.length > 0 && (
            <div className="text-right">
              <div className="flex gap-2 text-sm mb-2">
                <span className="flex items-center gap-2 px-3 py-1 bg-blue-50 text-blue-700 rounded-full border border-blue-200">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  IGDB Database
                </span>
                <span className="flex items-center gap-2 px-3 py-1 bg-primary/10 text-primary rounded-full border border-primary/20">
                  <span className="w-2 h-2 bg-primary rounded-full"></span>
                  Enhanced Search
                </span>
              </div>
              <p className="text-xs text-gray-500">Click any game for details</p>
            </div>
          )}
        </div>
      </div>
      
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-4 animate-fade-in">
        {games.map((game, index) => {
          const handleClick = () => handleGameClick(game);
          return (
            <div
              key={game.id}
              className="animate-slide-up"
              style={{ animationDelay: `${Math.min(index * 0.05, 2)}s` }}
            >
              <GameCard game={game} onClick={handleClick} />
            </div>
          );
        })}
      </div>

      <GameModal
        game={selectedGame}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />

      {/* Back to Top Button */}
      {showBackToTop && games.length > 12 && (
        <Button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 z-40 rounded-full w-12 h-12 p-0 shadow-lg hover:shadow-xl transition-all duration-300"
          aria-label="Back to top"
        >
          <ArrowUp className="h-5 w-5" />
        </Button>
      )}
    </div>
  );
}

interface GameCardProps {
  game: Game;
  onClick: () => void;
}

const GameCard = memo<GameCardProps>(({ game, onClick }) => {
  const addToLibrary = useAddToLibrary();
  const addToWishlist = useAddToWishlist();
  const { data: userGame } = useUserGameStatus(game?.id || '');

  const handleClick = useCallback(() => {
    onClick();
  }, [onClick]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClick();
    }
  }, [onClick]);

  const handleAddToLibrary = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addToLibrary.mutate(game);
  }, [addToLibrary, game]);

  const handleAddToWishlist = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addToWishlist.mutate(game);
  }, [addToWishlist, game]);

  return (
    <Card
      className="group cursor-pointer transition-all duration-300 hover:scale-[1.03] hover:shadow-2xl border-border/50 hover:border-primary/50 overflow-hidden bg-gradient-to-br from-background to-background/80 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`View details for ${game.title}`}
    >
      {/* Cover Image - Fixed aspect ratio */}
      <div className="aspect-[2/3] bg-gradient-to-br from-muted/50 to-muted/80 relative overflow-hidden">
        {game.cover_image ? (
          <img
            src={game.cover_image}
            alt={game.title}
            className="w-full h-full object-cover object-center transition-transform duration-300 group-hover:scale-105"
            style={{
              objectFit: 'cover',
              objectPosition: 'center top'
            }}
            loading="lazy"
            decoding="async"
            onLoad={(e) => {
              // Add fade-in effect when image loads
              const img = e.currentTarget;
              img.style.opacity = '0';
              img.style.transition = 'opacity 0.3s ease-in-out';
              requestAnimationFrame(() => {
                img.style.opacity = '1';
              });
            }}
            onError={(e) => {
              const target = e.currentTarget;
              target.style.display = 'none';
              const parent = target.parentElement;
              if (parent) {
                parent.innerHTML = `
                  <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-muted to-muted/80">
                    <div class="text-center text-muted-foreground">
                      <div class="mb-2"><svg class="h-12 w-12 mx-auto" fill="currentColor" viewBox="0 0 24 24"><path d="M21 6H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-10 7.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm4-3c-.83 0-1.5-.67-1.5-1.5S14.17 8.5 15 8.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/></svg></div>
                      <div class="text-sm font-medium">No Cover Art</div>
                    </div>
                  </div>
                `;
              }
            }}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-muted/40 to-muted/60">
            <div className="text-center text-muted-foreground">
              <Gamepad2 className="h-10 w-10 mx-auto mb-2" />
              <div className="text-xs font-medium">No Cover Art</div>
            </div>
          </div>
        )}

        {/* Enhanced gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        
        {/* Quick Action Buttons */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 z-20">
          <div className="flex flex-col gap-2">
            {!userGame ? (
              <>
                <Button
                  size="sm"
                  onClick={handleAddToLibrary}
                  disabled={addToLibrary.isPending}
                  className="bg-primary/90 hover:bg-primary text-white shadow-lg"
                >
                  {addToLibrary.isPending ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : addToLibrary.isSuccess ? (
                    <CheckCircle className="h-3 w-3" />
                  ) : (
                    <Plus className="h-3 w-3" />
                  )}
                  <span className="ml-1 text-xs">
                    {addToLibrary.isSuccess ? 'Added' : 'Library'}
                  </span>
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleAddToWishlist}
                  disabled={addToWishlist.isPending}
                  className="bg-background/90 hover:bg-background border-primary/50 text-primary shadow-lg"
                >
                  {addToWishlist.isPending ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : addToWishlist.isSuccess ? (
                    <CheckCircle className="h-3 w-3" />
                  ) : (
                    <Heart className="h-3 w-3" />
                  )}
                  <span className="ml-1 text-xs">
                    {addToWishlist.isSuccess ? 'Added' : 'Wishlist'}
                  </span>
                </Button>
              </>
            ) : (
              <Badge variant="secondary" className="bg-green-500/90 text-white border-green-400 shadow-lg">
                <CheckCircle className="h-3 w-3 mr-1" />
                <span className="text-xs">
                  {userGame.status === 'wishlist' ? 'In Wishlist' : 'In Library'}
                </span>
              </Badge>
            )}
          </div>
        </div>

        {/* Top badges - improved positioning */}
        <div className="absolute top-2 left-2 right-2 flex justify-between items-start z-10">
          {/* Platform Badge - show primary platform */}
          {game.platforms && game.platforms.length > 0 && (
            <Badge variant="secondary" className="bg-background/90 text-foreground border-border/50 shadow-lg text-xs font-medium">
              {game.platforms[0] === 'PC (Microsoft Windows)' ? 'PC' : 
               game.platforms[0] === 'PlayStation 5' ? 'PS5' :
               game.platforms[0] === 'PlayStation 4' ? 'PS4' :
               game.platforms[0] === 'PlayStation 3' ? 'PS3' :
               game.platforms[0] === 'Xbox Series X|S' ? 'Xbox S|X' :
               game.platforms[0] === 'Xbox One' ? 'Xbox One' :
               game.platforms[0] === 'Nintendo Switch' ? 'Switch' :
               game.platforms[0].length > 8 ? game.platforms[0].substring(0, 8) + '...' : game.platforms[0]}
              {game.platforms.length > 1 && ` +${game.platforms.length - 1}`}
            </Badge>
          )}
          
          {/* Rating Badge - enhanced colors */}
          {game.metacritic_score && (
            <Badge className={`shadow-lg text-xs font-semibold ${
              game.metacritic_score >= 85
                ? 'bg-emerald-500 hover:bg-emerald-600 border-emerald-400 text-white' :
              game.metacritic_score >= 70
                ? 'bg-yellow-500 hover:bg-yellow-600 border-yellow-400 text-white' :
              game.metacritic_score >= 50
                ? 'bg-orange-500 hover:bg-orange-600 border-orange-400 text-white' :
                'bg-red-500 hover:bg-red-600 border-red-400 text-white'
            }`}>
              <Star className="h-3 w-3 mr-1" />
              {game.metacritic_score}
            </Badge>
          )}
        </div>

        {/* Source Badge */}
        <div className="absolute bottom-2 right-2 z-10">
          {game.id.startsWith('igdb_') ? (
            <Badge variant="secondary" className="bg-blue-600/90 text-white border-blue-400/50 hover:bg-blue-700/90 shadow-lg text-xs font-medium">
              IGDB
            </Badge>
          ) : game.id.startsWith('tgdb_') ? (
            <Badge variant="secondary" className="bg-green-600/90 text-white border-green-400/50 hover:bg-green-700/90 shadow-lg text-xs font-medium">
              TGDB
            </Badge>
          ) : (
            <Badge variant="secondary" className="bg-gray-600/90 text-white border-gray-400/50 hover:bg-gray-700/90 shadow-lg text-xs font-medium">
              API
            </Badge>
          )}
        </div>

        {/* Enhanced title overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/95 via-black/85 to-transparent text-white p-3 transform translate-y-full group-hover:translate-y-0 transition-all duration-300 ease-out">
          <h3 className="font-bold text-sm leading-tight line-clamp-2 mb-2 text-white">{game.title}</h3>
          
          {/* Platform badges */}
          {game.platforms && game.platforms.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-2">
              {game.platforms.slice(0, 3).map((platform, index) => (
                <Badge 
                  key={index} 
                  variant="secondary" 
                  className="bg-primary/80 text-white border-primary/50 text-xs px-2 py-0 h-5"
                >
                  {platform === 'PC (Microsoft Windows)' ? 'PC' : 
                   platform === 'PlayStation 5' ? 'PS5' :
                   platform === 'PlayStation 4' ? 'PS4' :
                   platform === 'PlayStation 3' ? 'PS3' :
                   platform === 'Xbox Series X|S' ? 'Xbox S|X' :
                   platform === 'Xbox One' ? 'Xbox One' :
                   platform === 'Nintendo Switch' ? 'Switch' :
                   platform.length > 8 ? platform.substring(0, 8) + '...' : platform}
                </Badge>
              ))}
              {game.platforms.length > 3 && (
                <Badge 
                  variant="secondary" 
                  className="bg-gray-600/80 text-white border-gray-500/50 text-xs px-2 py-0 h-5"
                >
                  +{game.platforms.length - 3}
                </Badge>
              )}
            </div>
          )}
          
          <div className="flex items-center justify-between text-xs text-gray-200">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{game.release_date ? new Date(game.release_date).getFullYear() : 'Unknown'}</span>
            </div>
            {game.developer && (
              <div className="flex items-center gap-1 truncate ml-2">
                <User className="h-3 w-3" />
                <span className="truncate max-w-20">{game.developer}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
});

GameCard.displayName = 'GameCard';
