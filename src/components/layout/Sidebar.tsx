import { Link, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Search,
  Library,
  Heart,
  BadgePercent,
  Gamepad2,
  Bot,
  ChevronRight,
  Sparkles,
  Menu,
} from 'lucide-react';
import { Badge } from '@/components/ui/base/badge';
import { Button } from '@/components/ui/base/button';
import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON>eader, SheetTitle, SheetTrigger } from '@/components/ui/base/sheet';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/base/tooltip';
import { useState } from 'react';

const NavLink = ({ 
  icon, 
  children, 
  to, 
  isActive,
  badge,
  isNew = false,
  onClick,
  tooltip
}: { 
  icon: React.ReactNode; 
  children: React.ReactNode; 
  to: string;
  isActive: boolean;
  badge?: string;
  isNew?: boolean;
  onClick?: () => void;
  tooltip?: string;
}) => {
  const linkContent = (
    <Link
      to={to}
      onClick={onClick}
      className={`flex items-center gap-3 rounded-xl px-4 py-3 transition-all duration-300 font-medium group relative overflow-hidden ${
        isActive
          ? 'bg-gradient-to-r from-primary via-primary/90 to-primary/80 text-primary-foreground shadow-lg shadow-primary/25 scale-[1.02] border border-primary/30'
          : 'text-muted-foreground hover:text-primary hover:bg-gradient-to-r hover:from-accent/20 hover:to-primary/10 hover:scale-[1.02] hover:shadow-md border border-transparent hover:border-primary/20'
      }`}
    >
      <span className={`transition-all duration-300 group-hover:scale-110 ${isActive ? 'drop-shadow-sm' : ''}`}>
        {icon}
      </span>
      <span className="relative z-10 flex-1">{children}</span>
      <div className="flex items-center gap-2">
        {badge && (
          <Badge variant="secondary" className="text-xs px-2 py-0.5 bg-primary/15 text-primary border-primary/30 font-semibold">
            {badge}
          </Badge>
        )}
        {isNew && (
          <Badge variant="secondary" className="text-xs px-2 py-0.5 bg-emerald-100 text-emerald-700 border-emerald-200 animate-pulse font-semibold">
            <Sparkles className="h-3 w-3 mr-1" />
            New
          </Badge>
        )}
        <ChevronRight className={`h-4 w-4 transition-all duration-300 ${isActive ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-2 group-hover:opacity-60 group-hover:translate-x-0'}`} />
      </div>
      {isActive && (
        <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent opacity-50" />
      )}
    </Link>
  );

  if (tooltip) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          {linkContent}
        </TooltipTrigger>
        <TooltipContent side="right">
          {tooltip}
        </TooltipContent>
      </Tooltip>
    );
  }

  return linkContent;
};

const MobileNavLink = ({ 
  icon, 
  children, 
  to, 
  isActive,
  onClick
}: { 
  icon: React.ReactNode; 
  children: React.ReactNode; 
  to: string;
  isActive: boolean;
  onClick?: () => void;
}) => (
  <Link
    to={to}
    onClick={onClick}
    className={`flex items-center gap-4 rounded-lg px-4 py-3 transition-all duration-200 font-medium ${
      isActive
        ? 'bg-primary text-primary-foreground shadow-md'
        : 'text-muted-foreground hover:text-primary hover:bg-accent/50'
    }`}
  >
    <span className="transition-all duration-200">
      {icon}
    </span>
    <span className="flex-1">{children}</span>
  </Link>
);

export function Sidebar() {
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const closeMobileMenu = () => setMobileMenuOpen(false);

  const navigationItems = [
    { icon: <LayoutDashboard className="h-4 w-4" />, label: 'Dashboard', path: '/', tooltip: 'View your gaming dashboard' },
    { icon: <Search className="h-4 w-4" />, label: 'Search', path: '/search', tooltip: 'Search for games' },
    { icon: <Library className="h-4 w-4" />, label: 'Library', path: '/library', tooltip: 'Manage your game library' },
    { icon: <Heart className="h-4 w-4" />, label: 'Wishlist', path: '/wishlist', tooltip: 'Games you want to play' },
    { icon: <BadgePercent className="h-4 w-4" />, label: 'Deals', path: '/deals', tooltip: 'Find the best game deals' },
    { icon: <Bot className="h-4 w-4" />, label: 'AI Assistant', path: '/ai-agent', tooltip: 'Get AI-powered game recommendations', isNew: true },
  ];

  const accountItems = [
    // Settings and Profile moved to footer for cleaner navigation
  ];

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden border-r border-border bg-background md:block w-[280px] shadow-xl">
        <div className="flex h-full min-h-screen flex-col gap-2">
          {/* Logo Section */}
          <div className="flex h-16 items-center border-b border-border/50 px-4 lg:px-6 bg-gradient-to-r from-primary/5 via-secondary/5 to-accent/5">
            <Link to="/" className="flex items-center gap-3 font-bold text-xl group transition-all duration-300 hover:scale-105">
              <div className="p-2.5 bg-gradient-to-br from-primary/15 via-secondary/10 to-accent/15 rounded-xl border border-primary/30 group-hover:border-primary/50 transition-all duration-300 shadow-sm group-hover:shadow-md">
                <Gamepad2 className="h-6 w-6 text-primary group-hover:scale-110 transition-transform duration-300" />
              </div>
              <span className="bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent font-extrabold tracking-tight">
                Game Library
              </span>
            </Link>
          </div>

          {/* Main Navigation */}
          <div className="flex-1 px-3 py-6">
            <div className="space-y-2">
              <div className="px-3 py-2">
                <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Navigation</h3>
              </div>
              <nav className="grid items-start gap-1 text-sm font-medium">
                {navigationItems.map((item) => (
                  <NavLink
                    key={item.path}
                    icon={item.icon}
                    to={item.path}
                    isActive={location.pathname === item.path}
                    tooltip={item.tooltip}
                    isNew={item.isNew}
                  >
                    {item.label}
                  </NavLink>
                ))}
              </nav>
            </div>
          </div>

          {/* Account Section - Only show if there are items */}
          {accountItems.length > 0 && (
            <div className="mt-auto p-4 border-t border-border/50 bg-gradient-to-r from-muted/30 via-muted/20 to-muted/30">
              <div className="space-y-2">
                <div className="px-3 py-2">
                  <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Account</h3>
                </div>
                <nav className="grid items-start gap-1 text-sm font-medium">
                  {accountItems.map((item) => (
                    <NavLink
                      key={item.path}
                      icon={item.icon}
                      to={item.path}
                      isActive={location.pathname === item.path}
                      tooltip={item.tooltip}
                    >
                      {item.label}
                    </NavLink>
                  ))}
                </nav>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Menu Button */}
      <div className="md:hidden">
        <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
          <SheetTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="fixed top-4 left-4 z-50 bg-background border border-border shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle navigation menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-[280px] p-0 bg-background">
            <SheetHeader className="border-b border-border/50 p-6 bg-gradient-to-r from-primary/5 to-secondary/5">
              <SheetTitle className="flex items-center gap-3 text-left">
                <div className="p-2 bg-gradient-to-br from-primary/15 to-secondary/15 rounded-lg border border-primary/30">
                  <Gamepad2 className="h-5 w-5 text-primary" />
                </div>
                <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent font-bold">
                  Game Library
                </span>
              </SheetTitle>
            </SheetHeader>
            
            <div className="flex flex-col h-full">
              {/* Main Navigation */}
              <div className="flex-1 p-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3">Navigation</h3>
                    <nav className="space-y-1">
                      {navigationItems.map((item) => (
                        <MobileNavLink
                          key={item.path}
                          icon={item.icon}
                          to={item.path}
                          isActive={location.pathname === item.path}
                          onClick={closeMobileMenu}
                        >
                          {item.label}
                        </MobileNavLink>
                      ))}
                    </nav>
                  </div>
                </div>
              </div>

              {/* Account Section - Only show if there are items */}
              {accountItems.length > 0 && (
                <div className="mt-auto p-6 border-t border-border/50 bg-gradient-to-r from-muted/20 to-muted/10">
                  <div>
                    <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3">Account</h3>
                    <nav className="space-y-1">
                      {accountItems.map((item) => (
                        <MobileNavLink
                          key={item.path}
                          icon={item.icon}
                          to={item.path}
                          isActive={location.pathname === item.path}
                          onClick={closeMobileMenu}
                        >
                          {item.label}
                        </MobileNavLink>
                      ))}
                    </nav>
                  </div>
                </div>
              )}
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </>
  );
}