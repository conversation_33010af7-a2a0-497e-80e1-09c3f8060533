import { useMemo, useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base';
import { Button } from '@/components/ui/base/button';
import { UnifiedGameCard } from '@/components/ui/game/unified-game-card';
import { PlatformGroup } from '@/components/ui/utils';
import { PlatformFamilyView } from '../ViewModes/PlatformFamilyView';
import { SearchControls } from '../SearchControls';
import { VirtualizedLibraryGrid, VIRTUALIZATION_THRESHOLD } from '@/components/ui/library/virtualized-library-grid';
import { BulkOperationsToolbar, useBulkOperationShortcuts } from '@/components/ui/library/bulk-operations-toolbar';
import { ErrorBoundaryWrapper } from '@/components/ui/error-boundary';
import { Gamepad2, Search, Zap, CheckCircle } from '@/lib/icons';
import { LibraryTabProps } from '../../types';
import { UserGameWithDetails } from '@/types/database';
import { sortUserGames } from '@/lib/sortUtils';

export function LibraryTab({
  libraryGames,
  viewMode,
  searchQuery,
  setSearchQuery,
  sortOptions,
  setSortOptions,
  collapsedPlatforms,
  onPlatformToggle,
  collapsedFamilies,
  onFamilyToggle,
  onGameClick,
  onStatusUpdate,
  onRemoveGame
}: LibraryTabProps) {

  // Bulk operations state
  const [selectedGames, setSelectedGames] = useState<Set<string>>(new Set());
  const [enableBulkMode, setEnableBulkMode] = useState(false);

  // Handle game selection
  const handleGameSelect = useCallback((gameId: string, selected: boolean) => {
    setSelectedGames(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(gameId);
      } else {
        newSet.delete(gameId);
      }
      return newSet;
    });
  }, []);

  // Filter games based on search query
  const filteredLibraryGames = useMemo(() => {
    if (!searchQuery.trim()) return libraryGames;
    
    return libraryGames.filter((game: unknown) => {
      const gameData = game as { game?: { title?: string; developer?: string; publisher?: string; genres?: string[] } };
      const title = gameData?.game?.title?.toLowerCase() || '';
      const developer = gameData?.game?.developer?.toLowerCase() || '';
      const publisher = gameData?.game?.publisher?.toLowerCase() || '';
      const genres = gameData?.game?.genres?.join(' ').toLowerCase() || '';
      const query = searchQuery.toLowerCase();
      
      return title.includes(query) || 
             developer.includes(query) || 
             publisher.includes(query) || 
             genres.includes(query);
    });
  }, [libraryGames, searchQuery]);
  
  // Sort games based on current sort options
  const sortedLibraryGames = useMemo(() => {
    return sortUserGames(filteredLibraryGames, sortOptions);
  }, [filteredLibraryGames, sortOptions]);

  // Group games by platform
  const gamesByPlatform = useMemo(() => {
    const grouped: { [key: string]: UserGameWithDetails[] } = {};
    
    sortedLibraryGames.forEach((game) => {
      const typedGame = game as UserGameWithDetails;
      const platform = typedGame.game?.platform || 'Unknown';
      
      // Add game to its platform
      if (!grouped[platform]) {
        grouped[platform] = [];
      }
      grouped[platform].push(typedGame);
    });
    
    // Sort platforms by game count (descending)
    const sortedPlatforms = Object.keys(grouped).sort((a, b) => 
      grouped[b].length - grouped[a].length
    );
    
    const result: { [key: string]: UserGameWithDetails[] } = {};
    sortedPlatforms.forEach(platform => {
      result[platform] = grouped[platform];
    });
    
    return result;
  }, [sortedLibraryGames]);

  const handleTogglePlatformCollapse = (platform: string) => {
    onPlatformToggle(platform);
  };

  // Bulk operation handlers - must be defined after sortedLibraryGames
  const handleSelectAll = useCallback(() => {
    const allGameIds = new Set<string>(sortedLibraryGames.map((game: unknown) => {
      const gameData = game as UserGameWithDetails;
      return gameData.id;
    }));
    setSelectedGames(allGameIds);
  }, [sortedLibraryGames]);

  const handleSelectNone = useCallback(() => {
    setSelectedGames(new Set());
  }, []);

  const handleBulkStatusUpdate = useCallback((status: string) => {
    selectedGames.forEach(gameId => {
      onStatusUpdate(gameId, status);
    });
    setSelectedGames(new Set()); // Clear selection after update
  }, [selectedGames, onStatusUpdate]);

  const handleBulkRemove = useCallback(() => {
    selectedGames.forEach(gameId => {
      onRemoveGame(gameId);
    });
    setSelectedGames(new Set()); // Clear selection after removal
  }, [selectedGames, onRemoveGame]);

  const handleBulkRate = useCallback((rating: number) => {
    // This would need implementation in the parent component
    console.log('Bulk rating:', rating, 'for games:', Array.from(selectedGames));
    setSelectedGames(new Set()); // Clear selection after rating
  }, [selectedGames]);

  // Keyboard shortcuts for bulk operations
  const shortcutHandlers = useBulkOperationShortcuts(
    selectedGames.size,
    handleSelectAll,
    handleSelectNone,
    handleBulkStatusUpdate,
    handleBulkRemove
  );

  // Enable keyboard shortcuts when bulk mode is active
  useEffect(() => {
    if (enableBulkMode && selectedGames.size > 0) {
      const handleKeyDown = (e: KeyboardEvent) => {
        shortcutHandlers.onKeyDown(e);
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [enableBulkMode, selectedGames.size, shortcutHandlers]);

  // Toggle bulk mode when Escape is pressed
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && enableBulkMode) {
        setEnableBulkMode(false);
        setSelectedGames(new Set());
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [enableBulkMode]);

  // Determine if we should use virtualization based on collection size
  const shouldUseVirtualization = sortedLibraryGames.length > VIRTUALIZATION_THRESHOLD;

  return (
    <div className="space-y-6">
      {/* Search and Sort Controls */}
      {libraryGames.length > 0 && (
        <Card>
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Your Collection</CardTitle>
                <CardDescription>Games you've added to your library</CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant={enableBulkMode ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => {
                    setEnableBulkMode(!enableBulkMode);
                    if (enableBulkMode) {
                      setSelectedGames(new Set());
                    }
                  }}
                  className="gap-2"
                >
                  <CheckCircle className="h-4 w-4" />
                  {enableBulkMode ? 'Exit Bulk Mode' : 'Bulk Select'}
                </Button>
              </div>
            </div>
            <SearchControls
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              sortOptions={sortOptions}
              onSortChange={setSortOptions}
              onClearSearch={() => setSearchQuery('')}
            />
            {shouldUseVirtualization && (
              <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
                <Zap className="h-4 w-4" />
                <span>Virtualization enabled for better performance ({sortedLibraryGames.length} games)</span>
              </div>
            )}
          </CardHeader>
        </Card>
      )}

      {/* Library Content */}
      {libraryGames.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Gamepad2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">Your library is empty</p>
              <p className="text-sm text-muted-foreground mt-2">
                Search for games and add them to your collection
              </p>
            </div>
          </CardContent>
        </Card>
      ) : sortedLibraryGames.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Search className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">No games found</p>
              <p className="text-sm text-muted-foreground mt-2">
                Try adjusting your search terms
              </p>
            </div>
          </CardContent>
        </Card>
      ) : viewMode === 'platform' ? (
        <div className="space-y-4">
          {Object.entries(gamesByPlatform).map(([platform, games]) => (
            <PlatformGroup
              key={platform}
              platform={platform}
              games={games}
              onGameClick={onGameClick}
              onUpdateStatus={onStatusUpdate}
              onRemoveGame={onRemoveGame}
              isCollapsed={collapsedPlatforms.has(platform)}
              onToggleCollapse={() => handleTogglePlatformCollapse(platform)}
            />
          ))}
        </div>
      ) : viewMode === 'platform-family' ? (
        <ErrorBoundaryWrapper name="Platform Family View">
          <PlatformFamilyView
            games={libraryGames as UserGameWithDetails[]}
            collapsedFamilies={collapsedFamilies}
            onFamilyToggle={onFamilyToggle}
            onGameClick={onGameClick}
            onStatusUpdate={onStatusUpdate}
            onRemoveGame={onRemoveGame}
            searchQuery={searchQuery}
            sortOptions={sortOptions}
          />
        </ErrorBoundaryWrapper>
      ) : (
        shouldUseVirtualization ? (
          <ErrorBoundaryWrapper name="Virtualized Library Grid">
            <div className="h-96 w-full">
              <VirtualizedLibraryGrid
                games={sortedLibraryGames as UserGameWithDetails[]}
                onGameClick={onGameClick}
                onStatusUpdate={onStatusUpdate}
                onRemoveGame={onRemoveGame}
                onSelect={handleGameSelect}
                selectedGames={selectedGames}
                enableSelection={enableBulkMode}
              />
            </div>
          </ErrorBoundaryWrapper>
        ) : (
          <ErrorBoundaryWrapper name="Game Library Grid">
            <Card>
              <CardContent className="pt-6">
                <div className="grid gap-3 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 3xl:grid-cols-7">
                  {sortedLibraryGames.map((userGame: unknown) => {
                    const gameData = userGame as UserGameWithDetails;
                    return (
                      <ErrorBoundaryWrapper key={gameData.id} name="Game Card">
                        <UnifiedGameCard
                          gameData={gameData}
                          variant="standard"
                          style="modern"
                          animationLevel="dynamic"
                          showQuickActions={true}
                          enableHoverEffects={true}
                          enableSelection={enableBulkMode}
                          isSelected={selectedGames.has(gameData.id)}
                          onGameClick={onGameClick}
                          onStatusUpdate={onStatusUpdate}
                          onRemoveGame={onRemoveGame}
                          onSelect={handleGameSelect}
                        />
                      </ErrorBoundaryWrapper>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </ErrorBoundaryWrapper>
        )
      )}

      {/* Bulk Operations Toolbar */}
      {enableBulkMode && (
        <BulkOperationsToolbar
          selectedCount={selectedGames.size}
          totalCount={sortedLibraryGames.length}
          onSelectAll={handleSelectAll}
          onSelectNone={handleSelectNone}
          onBulkStatusUpdate={handleBulkStatusUpdate}
          onBulkRemove={handleBulkRemove}
          onBulkRate={handleBulkRate}
        />
      )}
    </div>
  );
}