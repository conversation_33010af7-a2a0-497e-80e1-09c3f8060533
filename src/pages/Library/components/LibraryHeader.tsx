import { <PERSON><PERSON>, Button } from '@/components/ui/base';
import { Grid3X3, Layers, Users } from '@/lib/icons';
import { ViewMode } from '../types';

interface LibraryHeaderProps {
  gameCount: number;
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
}

export function LibraryHeader({ gameCount, viewMode, onViewModeChange }: LibraryHeaderProps) {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 className="text-2xl sm:text-3xl font-bold">My Library</h1>
        <p className="text-muted-foreground">Track your gaming collection</p>
      </div>
      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
        <Badge variant="outline" className="bg-primary/10 w-fit">
          {gameCount} games
        </Badge>
        <div className="flex items-center gap-1 bg-muted rounded-lg p-1 w-full sm:w-auto overflow-x-auto">
          <Button
            variant={viewMode === 'status' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => onViewModeChange('status')}
            className="h-8 flex-shrink-0"
          >
            <Grid3X3 className="h-4 w-4 mr-1" />
            <span>Status</span>
          </Button>
          <Button
            variant={viewMode === 'platform' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => onViewModeChange('platform')}
            className="h-8 flex-shrink-0"
          >
            <Layers className="h-4 w-4 mr-1" />
            <span>Platform</span>
          </Button>
          <Button
            variant={viewMode === 'platform-family' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => onViewModeChange('platform-family')}
            className="h-8 flex-shrink-0"
          >
            <Users className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">Platform Family</span>
            <span className="sm:hidden">Family</span>
          </Button>
        </div>
      </div>
    </div>
  );
}