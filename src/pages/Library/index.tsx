import { useState, useEffect, useRef } from 'react';
import { LoadingSpinner } from '@/components/ui/utils';
import { GameModal } from '@/components/ui/game';
import { useUpdateGameStatus, useRemoveFromCollection } from '@/hooks/useGameActions';
import { useViewModeState } from '@/hooks/useViewModeState';
import { Game } from '@/types';
import { UserGameWithDetails } from '@/types/database';
import { LibraryHeader } from './components/LibraryHeader';
import { LibraryStats } from './components/LibraryStats';
import { LibraryTabs, TabContent } from './components/LibraryTabs';
import { LibraryTab } from './components/LibraryTabs/LibraryTab';
import { CollectionsStatsTab } from './components/LibraryTabs/CollectionsStatsTab';
import { AnalyticsTab } from './components/LibraryTabs/AnalyticsTab';
import { ManageTab } from './components/LibraryTabs/ManageTab';
import { useLibraryData } from './hooks/useLibraryData';
import { LibraryFilters, ViewMode } from './types';
import { 
  preserveViewContext, 
  shouldResetScrollOnTransition,
  isValidViewModeTransition,
  ViewModeContext 
} from '@/lib/utils/viewModeTransitions';

export default function Library() {
  const { libraryGames, stats, isLoading, error } = useLibraryData();
  const { 
    viewMode, 
    collapsedPlatforms, 
    collapsedFamilies, 
    setViewMode: setViewModeInternal, 
    togglePlatform, 
    toggleFamily,
    isLoading: viewModeLoading 
  } = useViewModeState();
  const updateGameStatus = useUpdateGameStatus();
  const removeFromCollection = useRemoveFromCollection();
  
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [sortOptions, setSortOptions] = useState<{ field: string; direction: 'asc' | 'desc' }>({
    field: 'added_at',
    direction: 'desc'
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('library');
  const [analyticsTimeframe, setAnalyticsTimeframe] = useState<'week' | 'month' | 'quarter' | 'year'>('month');
  const [filters, setFilters] = useState<LibraryFilters>({});
  
  // Ref to track previous view mode for smooth transitions
  const previousViewMode = useRef<ViewMode>(viewMode);
  const containerRef = useRef<HTMLDivElement>(null);

  // Handle view mode changes with context preservation
  const handleViewModeChange = (newMode: ViewMode) => {
    const currentMode = previousViewMode.current;
    
    // Validate the transition
    if (!isValidViewModeTransition(currentMode, newMode)) {
      console.warn(`Invalid view mode transition from ${currentMode} to ${newMode}`);
      return;
    }

    // Create current context
    const currentContext: ViewModeContext = {
      searchQuery,
      sortOptions,
      filters,
      selectedGameId: selectedGame?.id,
      scrollPosition: containerRef.current?.scrollTop || 0,
    };

    // Preserve context during transition
    const preservedContext = preserveViewContext(currentMode, newMode, currentContext);

    // Apply preserved context
    setSearchQuery(preservedContext.searchQuery);
    setSortOptions(preservedContext.sortOptions);
    setFilters(preservedContext.filters);

    // Handle scroll position
    if (shouldResetScrollOnTransition(currentMode, newMode)) {
      // Reset scroll to top for major transitions
      setTimeout(() => {
        containerRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
      }, 100);
    }

    // Update view mode
    setViewModeInternal(newMode);
    previousViewMode.current = newMode;
  };

  // Update previous view mode ref when view mode changes
  useEffect(() => {
    previousViewMode.current = viewMode;
  }, [viewMode]);

  const handleGameClick = (gameData: UserGameWithDetails) => {
    // Convert user_game data to Game format
    const game: Game = {
      id: gameData.game?.id || gameData.game_id || '',
      title: gameData.game?.title || 'Unknown Game',
      platforms: gameData.game?.platforms,
      genres: gameData.game?.genres || [],
      developer: gameData.game?.developer,
      publisher: gameData.game?.publisher,
      release_date: gameData.game?.release_date,
      description: gameData.game?.description,
      cover_image: gameData.game?.cover_image,
      screenshots: gameData.game?.screenshots || [],
      youtube_links: gameData.game?.youtube_links || [],
      metacritic_score: gameData.game?.metacritic_score,
      igdb_id: gameData.game?.igdb_id,
    };
    
    setSelectedGame(game);
    setIsModalOpen(true);
  };

  const handleStatusUpdate = (userGameId: string, newStatus: string) => {
    updateGameStatus.mutate({
      userGameId,
      status: newStatus
    });
  };

  const handleRemoveFromCollection = (userGameId: string) => {
    removeFromCollection.mutate(userGameId);
  };

  const handlePlatformToggle = togglePlatform;
  const handleFamilyToggle = toggleFamily;

  const handleExport = async (format: string, options: Record<string, unknown>) => {
    console.log('Exporting library:', format, options);
    // Implementation will be added
  };
  
  const handleImport = async (source: string, data: unknown, options: Record<string, unknown>) => {
    console.log('Importing from:', source, options);
    // Implementation will be added
  };

  const handleFiltersApply = (newFilters: LibraryFilters) => {
    setFilters(newFilters);
    console.log('Applying filters:', newFilters);
    // Implementation will be added
  };

  const handleCollectionCreate = (collection: Record<string, unknown>) => {
    console.log('Creating collection:', collection);
    // Implementation will be added
  };

  const handleCollectionUpdate = (id: string, updates: Record<string, unknown>) => {
    console.log('Updating collection:', id, updates);
    // Implementation will be added
  };

  const handleCollectionDelete = (id: string) => {
    console.log('Deleting collection:', id);
    // Implementation will be added
  };

  const handleCollectionDuplicate = (id: string) => {
    console.log('Duplicating collection:', id);
    // Implementation will be added
  };

  if (isLoading || viewModeLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="text-center py-8">
          <p className="text-destructive">Failed to load library</p>
        </div>
      </div>
    );
  }

  return (
    <div ref={containerRef} className="container mx-auto p-6 space-y-6">
      <LibraryHeader 
        gameCount={libraryGames.length}
        viewMode={viewMode}
        onViewModeChange={handleViewModeChange}
      />
      
      <LibraryTabs activeTab={activeTab} onTabChange={setActiveTab}>
        <TabContent value="library">
          <LibraryStats stats={stats} isLoading={isLoading} />
          <LibraryTab
            libraryGames={libraryGames}
            isLoading={isLoading}
            error={error}
            viewMode={viewMode}
            setViewMode={handleViewModeChange}
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            sortOptions={sortOptions}
            setSortOptions={setSortOptions}
            collapsedPlatforms={collapsedPlatforms}
            onPlatformToggle={handlePlatformToggle}
            collapsedFamilies={collapsedFamilies}
            onFamilyToggle={handleFamilyToggle}
            onGameClick={handleGameClick}
            onStatusUpdate={handleStatusUpdate}
            onRemoveGame={handleRemoveFromCollection}
          />
        </TabContent>
        
        <TabContent value="collections">
          <CollectionsStatsTab
            libraryGames={libraryGames}
            isLoading={isLoading}
            error={error}
            stats={stats}
            onCollectionCreate={handleCollectionCreate}
            onCollectionUpdate={handleCollectionUpdate}
            onCollectionDelete={handleCollectionDelete}
            onCollectionDuplicate={handleCollectionDuplicate}
          />
        </TabContent>
        
        <TabContent value="analytics">
          <AnalyticsTab
            libraryGames={libraryGames}
            isLoading={isLoading}
            error={error}
            timeframe={analyticsTimeframe}
            onTimeframeChange={setAnalyticsTimeframe}
          />
        </TabContent>
        
        <TabContent value="manage">
          <ManageTab
            libraryGames={libraryGames}
            isLoading={isLoading}
            error={error}
            filters={filters}
            onFiltersApply={handleFiltersApply}
            onExport={handleExport}
            onImport={handleImport}
          />
        </TabContent>
      </LibraryTabs>

      <GameModal
        game={selectedGame}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
}