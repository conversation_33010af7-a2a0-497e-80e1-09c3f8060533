import { Badge } from '@/components/ui/base';
import { Heart } from 'lucide-react';

interface WishlistHeaderProps {
  gameCount: number;
}

export function WishlistHeader({ gameCount }: WishlistHeaderProps) {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold">Wishlist</h1>
        <p className="text-muted-foreground">Games you want to play</p>
      </div>
      <Badge variant="outline" className="bg-secondary/10 text-secondary-foreground">
        <Heart className="h-3 w-3 mr-1" />
        {gameCount} games
      </Badge>
    </div>
  );
}