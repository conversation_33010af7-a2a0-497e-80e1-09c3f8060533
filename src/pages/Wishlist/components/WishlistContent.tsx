import { useState, useMemo } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle, 
  Badge, 
  Button, 
  Input, 
  DropdownMenu, 
  DropdownMenuItem 
} from '@/components/ui/base';
import { SortOptionsComponent, SortOptions } from '@/components/ui/filters';
import { useAddToLibrary, useRemoveFromCollection } from '@/hooks/useGameActions';
import { useMoveToLibrary } from '@/hooks/useMoveToLibrary';
import { Game } from '@/types';
import { UserGameWithDetails } from '@/types/database';
import { sortUserGames } from '@/lib/sortUtils';
import {
  Heart,
  Plus,
  Gamepad2,
  Calendar,
  Star,
  Loader2,
  TrendingDown,
  MoreVertical,
  Trash2,
  Search,
  ShoppingCart
} from 'lucide-react';

interface WishlistContentProps {
  wishlistGames: UserGameWithDetails[];
  onGameClick: (gameData: UserGameWithDetails) => void;
}

export function WishlistContent({ wishlistGames, onGameClick }: WishlistContentProps) {
  const addToLibrary = useAddToLibrary();
  const moveToLibrary = useMoveToLibrary();
  const removeFromCollection = useRemoveFromCollection();
  const [sortOptions, setSortOptions] = useState<SortOptions>({
    field: 'added_at',
    direction: 'desc'
  });
  const [searchQuery, setSearchQuery] = useState('');

  // Filter games based on search query
  const filteredWishlistGames = useMemo(() => {
    if (!searchQuery.trim()) return wishlistGames;
    
    return wishlistGames.filter((game: UserGameWithDetails) => {
      const gameData = game;
      const title = gameData?.game?.title?.toLowerCase() || '';
      const developer = gameData?.game?.developer?.toLowerCase() || '';
      const publisher = gameData?.game?.publisher?.toLowerCase() || '';
      const genres = gameData?.game?.genres?.join(' ').toLowerCase() || '';
      const query = searchQuery.toLowerCase();
      
      return title.includes(query) || 
             developer.includes(query) || 
             publisher.includes(query) || 
             genres.includes(query);
    });
  }, [wishlistGames, searchQuery]);
  
  // Sort games based on current sort options
  const sortedWishlistGames = useMemo(() => {
    return sortUserGames(filteredWishlistGames, sortOptions);
  }, [filteredWishlistGames, sortOptions]);

  const handleAddToLibrary = (gameData: UserGameWithDetails) => {
    const game: Game = {
      id: gameData.game?.id || gameData.game_id,
      title: gameData.game?.title || 'Unknown Game',
      platforms: gameData.game?.platforms,
      genres: gameData.game?.genres || [],
      developer: gameData.game?.developer,
      publisher: gameData.game?.publisher,
      release_date: gameData.game?.release_date,
      description: gameData.game?.description,
      cover_image: gameData.game?.cover_image,
      screenshots: gameData.game?.screenshots || [],
      youtube_links: gameData.game?.youtube_links || [],
      metacritic_score: gameData.game?.metacritic_score,
      igdb_id: gameData.game?.igdb_id,
    };
    
    addToLibrary.mutate(game);
  };

  const handleMoveToLibrary = (userGameId: string, gameData: UserGameWithDetails) => {
    const game: Game = {
      id: gameData.game?.id || gameData.game_id,
      title: gameData.game?.title || 'Unknown Game',
      platforms: gameData.game?.platforms,
      genres: gameData.game?.genres || [],
      developer: gameData.game?.developer,
      publisher: gameData.game?.publisher,
      release_date: gameData.game?.release_date,
      description: gameData.game?.description,
      cover_image: gameData.game?.cover_image,
      screenshots: gameData.game?.screenshots || [],
      youtube_links: gameData.game?.youtube_links || [],
      metacritic_score: gameData.game?.metacritic_score,
      igdb_id: gameData.game?.igdb_id,
    };
    
    moveToLibrary.mutate({ userGameId, game });
  };

  const handleRemoveFromWishlist = (userGameId: string) => {
    removeFromCollection.mutate(userGameId);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Your Wishlist</CardTitle>
            <CardDescription>Games you want to play</CardDescription>
          </div>
          {wishlistGames.length > 0 && (
            <SortOptionsComponent
              sortOptions={sortOptions}
              onSortChange={setSortOptions}
              showAddedAt={true}
            />
          )}
        </div>
        {wishlistGames.length > 0 && (
          <div className="flex items-center gap-4 pt-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search your wishlist..."
                className="pl-10"
              />
            </div>
            {searchQuery && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSearchQuery('')}
                className="shrink-0"
              >
                Clear
              </Button>
            )}
          </div>
        )}
      </CardHeader>
      <CardContent>
        {wishlistGames.length === 0 ? (
          <div className="text-center py-8">
            <Heart className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">Your wishlist is empty</p>
            <p className="text-sm text-muted-foreground mt-2">
              Add games to your wishlist to track them
            </p>
          </div>
        ) : sortedWishlistGames.length === 0 ? (
          <div className="text-center py-8">
            <Search className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">No games found</p>
            <p className="text-sm text-muted-foreground mt-2">
              Try adjusting your search terms
            </p>
          </div>
        ) : (
          <div className="grid gap-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6">
            {sortedWishlistGames.map((userGame) => {
              const typedUserGame = userGame as UserGameWithDetails;
              return (
              <Card
                key={typedUserGame.id}
                className="group cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => onGameClick(typedUserGame)}
              >
                <div className="aspect-[2/3] bg-gradient-to-br from-muted to-muted/80 relative overflow-hidden">
                  {typedUserGame.game?.cover_image ? (
                    <img
                      src={typedUserGame.game.cover_image}
                      alt={typedUserGame.game?.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                      loading="lazy"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <Gamepad2 className="h-8 w-8 text-muted-foreground" />
                    </div>
                  )}
                  
                  {/* Wishlist Badge */}
                  <div className="absolute top-1.5 left-1.5">
                    <Badge variant="secondary" className="text-xs bg-secondary/80 text-secondary-foreground">
                      <Heart className="h-3 w-3 mr-1" />
                      Wishlist
                    </Badge>
                  </div>

                  {/* Quick Actions */}
                  <div className="absolute top-1.5 right-1.5 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                    <Button 
                      variant="ghost" 
                      size="sm"
                      className="h-7 w-7 p-0 bg-destructive/80 hover:bg-destructive text-destructive-foreground"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveFromWishlist(typedUserGame.id);
                      }}
                      title="Remove from Wishlist"
                    >
                      <Trash2 className="h-3.5 w-3.5" />
                    </Button>
                    <DropdownMenu
                      trigger={
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="h-7 w-7 p-0 bg-background/80 hover:bg-background"
                          onClick={(e) => e.stopPropagation()}
                          title="Actions"
                        >
                          <MoreVertical className="h-3.5 w-3.5" />
                        </Button>
                      }
                    >
                      <DropdownMenuItem
                        onClick={() => handleMoveToLibrary(typedUserGame.id, typedUserGame)}
                        disabled={moveToLibrary.isPending}
                      >
                        {moveToLibrary.isPending ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <ShoppingCart className="h-4 w-4 mr-2" />
                        )}
                        Mark as Purchased
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleAddToLibrary(typedUserGame)}
                        disabled={addToLibrary.isPending}
                      >
                        {addToLibrary.isPending ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Plus className="h-4 w-4 mr-2" />
                        )}
                        Add to Library
                      </DropdownMenuItem>
                    </DropdownMenu>
                  </div>

                  {/* Price tracking indicator */}
                  <div className="absolute bottom-1.5 right-1.5 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Badge variant="secondary" className="text-xs bg-accent/80 text-accent-foreground">
                      <TrendingDown className="h-3 w-3 mr-1" />
                      Price Monitored
                    </Badge>
                  </div>
                </div>
                
                <div className="p-2.5">
                  <h3 className="font-medium text-xs leading-tight line-clamp-2 mb-1">
                    {typedUserGame.game?.title || 'Unknown Game'}
                  </h3>
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span className="text-xs">
                        {typedUserGame.game?.release_date ?
                          new Date(typedUserGame.game.release_date).getFullYear() :
                          'Unknown'
                        }
                      </span>
                    </div>
                    {typedUserGame.game?.metacritic_score && (
                      <div className="flex items-center gap-1">
                        <Star className="h-3 w-3" />
                        <span className="text-xs">{typedUserGame.game.metacritic_score}</span>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}