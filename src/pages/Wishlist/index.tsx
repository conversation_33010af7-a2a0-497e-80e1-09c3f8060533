import { useState } from 'react';
import { GameModal } from '@/components/ui/game';
import { LoadingSpinner } from '@/components/ui/utils';
import { Game } from '@/types';
import { UserGameWithDetails } from '@/types/database';
import { WishlistHeader } from './components/WishlistHeader';
import { WishlistStats } from './components/WishlistStats';
import { WishlistContent } from './components/WishlistContent';
import { useWishlistData } from './hooks';

export default function Wishlist() {
  const { wishlistGames, stats, isLoading, error } = useWishlistData();
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleGameClick = (gameData: UserGameWithDetails) => {
    // Convert user_game data to Game format
    const game: Game = {
      id: gameData.game?.id || gameData.game_id,
      title: gameData.game?.title || 'Unknown Game',
      platforms: gameData.game?.platforms,
      genres: gameData.game?.genres || [],
      developer: gameData.game?.developer,
      publisher: gameData.game?.publisher,
      release_date: gameData.game?.release_date,
      description: gameData.game?.description,
      cover_image: gameData.game?.cover_image,
      screenshots: gameData.game?.screenshots || [],
      youtube_links: gameData.game?.youtube_links || [],
      metacritic_score: gameData.game?.metacritic_score,
      igdb_id: gameData.game?.igdb_id,
    };
    
    setSelectedGame(game);
    setIsModalOpen(true);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="text-center py-8">
          <p className="text-destructive">Failed to load wishlist</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <WishlistHeader gameCount={wishlistGames.length} />
      
      <WishlistStats stats={stats} />
      
      <WishlistContent 
        wishlistGames={wishlistGames}
        onGameClick={handleGameClick}
      />

      <GameModal
        game={selectedGame}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
}